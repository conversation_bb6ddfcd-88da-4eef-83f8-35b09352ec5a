[tool.poetry]
name = "reflection-agent"
version = "0.1.0"
description = ""
authors = ["suryam76 <<EMAIL>>"]
readme = "README.md"
packages = [{include = "reflection_agent"}]

[tool.poetry.dependencies]
python = "^3.10"
python-dotenv = "^1.1.1"
black = "^25.1.0"
isort = "^6.0.1"
langchain = "^0.3.27"
langchain-openai = "^0.3.31"
langchain-anthropic = "^0.3.19"
langgraph = "^0.6.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
